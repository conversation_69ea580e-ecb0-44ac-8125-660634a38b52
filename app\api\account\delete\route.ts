import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { 
  sessionOperations, 
  subscriptionOperations, 
  oneTimePurchaseOperations 
} from "@/lib/database";
import { supabaseAdmin } from "@/lib/supabase";

/**
 * DELETE /api/account/delete
 * 
 * Permanently delete user account and all associated data
 * This is a GDPR-compliant hard delete that removes all user data
 * 
 * Security Requirements:
 * - User must be authenticated
 * - Password verification required
 * - All related data must be cleaned up
 * - Third-party services must be notified
 */
export async function DELETE(request: NextRequest) {
  try {
    // Get authenticated session
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { password, confirmation } = body;

    // Validate required fields
    if (!password) {
      return NextResponse.json(
        { error: "Password is required for account deletion" },
        { status: 400 }
      );
    }

    if (confirmation !== "DELETE FOREVER") {
      return NextResponse.json(
        { error: 'Please type "DELETE FOREVER" to confirm' },
        { status: 400 }
      );
    }

    const userId = session.user.id;
    const userEmail = session.user.email;

    console.log(`Starting account deletion for user: ${userId}`);

    // Step 1: Cancel all active subscriptions
    try {
      const userSubscriptions = await subscriptionOperations.findByUserId(userId);
      
      for (const subscription of userSubscriptions) {
        if (subscription.status === 'active') {
          try {
            // TODO: Implement actual payment provider cancellation
            // const { creem } = await import('@/lib/creem');
            // await creem.cancelSubscription({
            //   xApiKey: process.env.CREEM_API_KEY as string,
            //   id: subscription.id
            // });
            
            console.log(`Would cancel subscription: ${subscription.id}`);
          } catch (error) {
            console.error(`Failed to cancel subscription ${subscription.id}:`, error);
            // Continue with deletion even if subscription cancellation fails
          }
        }
      }
    } catch (error) {
      console.error('Error canceling subscriptions:', error);
    }

    // Step 2: Delete all related data using admin client
    try {
      // Delete in order to respect foreign key constraints
      
      // 1. Delete sessions
      await supabaseAdmin
        .from('sessions')
        .delete()
        .eq('user_id', userId);
      
      // 2. Delete OAuth accounts
      await supabaseAdmin
        .from('accounts')
        .delete()
        .eq('user_id', userId);
      
      // 3. Delete subscriptions
      await supabaseAdmin
        .from('subscriptions')
        .delete()
        .eq('user_id', userId);
      
      // 4. Delete one-time purchases
      await supabaseAdmin
        .from('one_time_purchases')
        .delete()
        .eq('user_id', userId);
      
      // 5. Delete verifications (by email since they don't have user_id)
      await supabaseAdmin
        .from('verifications')
        .delete()
        .eq('identifier', userEmail);

      console.log(`Deleted all related data for user: ${userId}`);

    } catch (error) {
      console.error('Error deleting related data:', error);
      return NextResponse.json(
        { error: "Failed to delete account data" },
        { status: 500 }
      );
    }

    // Step 3: Delete the user account using Better Auth
    try {
      // Use Better Auth's deleteUser method which will handle the user table deletion
      await auth.api.deleteUser({
        headers: headers(),
        body: { password }
      });

      console.log(`Successfully deleted user account: ${userId}`);

    } catch (error: any) {
      console.error('Error deleting user account:', error);
      return NextResponse.json(
        { error: error.message || "Failed to delete user account" },
        { status: 500 }
      );
    }

    // Step 4: Log successful deletion for audit purposes
    console.log('Account deletion completed:', {
      userId,
      email: userEmail,
      deletedAt: new Date().toISOString(),
      ip: request.headers.get('x-forwarded-for') || 'unknown'
    });

    return NextResponse.json({
      success: true,
      message: "Account permanently deleted"
    });

  } catch (error: any) {
    console.error('Account deletion error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/account/delete
 * 
 * Get information about what data will be deleted
 * This helps users understand the scope of deletion
 */
export async function GET() {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get counts of data that will be deleted
    const [subscriptions, purchases] = await Promise.all([
      subscriptionOperations.findByUserId(userId),
      oneTimePurchaseOperations.findByUserId(userId)
    ]);

    const deletionInfo = {
      user: {
        email: session.user.email,
        name: session.user.name,
        createdAt: session.user.createdAt
      },
      dataToDelete: {
        subscriptions: subscriptions.length,
        activeSubscriptions: subscriptions.filter(s => s.status === 'active').length,
        purchases: purchases.length,
        sessions: "All active sessions",
        oauthAccounts: "All connected accounts"
      },
      warning: "This action cannot be undone. All your data will be permanently deleted."
    };

    return NextResponse.json(deletionInfo);

  } catch (error: any) {
    console.error('Error getting deletion info:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
