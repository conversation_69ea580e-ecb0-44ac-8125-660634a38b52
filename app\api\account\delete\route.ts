import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import {
  sessionOperations,
  subscriptionOperations
} from "@/lib/database";
import { supabaseAdmin } from "@/lib/supabase";

/**
 * Soft delete implementation - anonymizes user data but retains records
 * Used when legal/business requirements prevent complete data deletion
 */
async function handleSoftDelete(userId: string, userEmail: string, request: NextRequest) {
  try {
    const anonymizedEmail = `deleted-${userId.slice(0, 8)}@anonymized.local`;
    const anonymizedName = `[DELETED USER]`;
    const deletionTimestamp = new Date().toISOString();

    // 1. Anonymize user data instead of deleting
    await supabaseAdmin
      .from('users')
      .update({
        email: anonymizedEmail,
        name: anonymizedName,
        image: null,
        email_verified: false,
        updated_at: deletionTimestamp
      })
      .eq('id', userId);

    // 2. Delete active sessions (user gets logged out)
    await supabaseAdmin
      .from('sessions')
      .delete()
      .eq('user_id', userId);

    // 3. Cancel active subscriptions but keep records for accounting
    const userSubscriptions = await subscriptionOperations.findByUserId(userId);
    for (const subscription of userSubscriptions) {
      if (subscription.status === 'active') {
        try {
          // TODO: Cancel with payment provider
          console.log(`Would cancel subscription: ${subscription.id}`);

          // Update status to cancelled but keep record
          await supabaseAdmin
            .from('subscriptions')
            .update({
              status: 'canceled',
              updated_at: deletionTimestamp
            })
            .eq('id', subscription.id);
        } catch (error) {
          console.error(`Failed to cancel subscription ${subscription.id}:`, error);
        }
      }
    }

    // 4. Keep purchase records for accounting/tax purposes but anonymize
    await supabaseAdmin
      .from('one_time_purchases')
      .update({
        updated_at: deletionTimestamp
        // Note: Keep purchase records for legal/tax requirements
      })
      .eq('user_id', userId);

    // 5. Delete OAuth accounts (no business need to retain)
    await supabaseAdmin
      .from('accounts')
      .delete()
      .eq('user_id', userId);

    // 6. Delete verification tokens (no need to retain)
    await supabaseAdmin
      .from('verifications')
      .delete()
      .eq('identifier', userEmail);

    // Log soft deletion for audit
    console.log('Soft deletion completed:', {
      userId,
      originalEmail: userEmail,
      anonymizedEmail,
      deletedAt: deletionTimestamp,
      ip: request.headers.get('x-forwarded-for') || 'unknown',
      type: 'soft_delete'
    });

    return NextResponse.json({
      success: true,
      message: "Account anonymized successfully. Personal data has been removed while preserving necessary business records.",
      type: 'soft_delete'
    });

  } catch (error: any) {
    console.error('Soft deletion error:', error);
    return NextResponse.json(
      { error: error.message || "Failed to anonymize account" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/account/delete
 *
 * Delete user account with configurable deletion type
 * - Hard delete: Permanently removes all data (GDPR compliant)
 * - Soft delete: Anonymizes data but retains for legal/business requirements
 *
 * Security Requirements:
 * - User must be authenticated
 * - Password verification required
 * - All related data must be cleaned up or anonymized
 * - Third-party services must be notified
 */
export async function DELETE(request: NextRequest) {
  try {
    // Get authenticated session
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const body = await request.json();
    const { password, confirmation, deletionType = 'hard' } = body;

    // Validate required fields
    if (!password) {
      return NextResponse.json(
        { error: "Password is required for account deletion" },
        { status: 400 }
      );
    }

    if (confirmation !== "DELETE FOREVER") {
      return NextResponse.json(
        { error: 'Please type "DELETE FOREVER" to confirm' },
        { status: 400 }
      );
    }

    const userId = session.user.id;
    const userEmail = session.user.email;

    console.log(`Starting ${deletionType} deletion for user: ${userId}`);

    if (deletionType === 'soft') {
      return await handleSoftDelete(userId, userEmail, request);
    }

    // Hard delete implementation
    // Step 1: Cancel all active subscriptions
    try {
      const userSubscriptions = await subscriptionOperations.findByUserId(userId);
      
      for (const subscription of userSubscriptions) {
        if (subscription.status === 'active') {
          try {
            // TODO: Implement actual payment provider cancellation
            // const { creem } = await import('@/lib/creem');
            // await creem.cancelSubscription({
            //   xApiKey: process.env.CREEM_API_KEY as string,
            //   id: subscription.id
            // });
            
            console.log(`Would cancel subscription: ${subscription.id}`);
          } catch (error) {
            console.error(`Failed to cancel subscription ${subscription.id}:`, error);
            // Continue with deletion even if subscription cancellation fails
          }
        }
      }
    } catch (error) {
      console.error('Error canceling subscriptions:', error);
    }

    // Step 2: Delete all related data using admin client
    try {
      // Delete in order to respect foreign key constraints
      
      // 1. Delete sessions
      await supabaseAdmin
        .from('sessions')
        .delete()
        .eq('user_id', userId);
      
      // 2. Delete OAuth accounts
      await supabaseAdmin
        .from('accounts')
        .delete()
        .eq('user_id', userId);
      
      // 3. Delete subscriptions
      await supabaseAdmin
        .from('subscriptions')
        .delete()
        .eq('user_id', userId);
      
      // 4. Delete one-time purchases
      await supabaseAdmin
        .from('one_time_purchases')
        .delete()
        .eq('user_id', userId);
      
      // 5. Delete verifications (by email since they don't have user_id)
      await supabaseAdmin
        .from('verifications')
        .delete()
        .eq('identifier', userEmail);

      console.log(`Deleted all related data for user: ${userId}`);

    } catch (error) {
      console.error('Error deleting related data:', error);
      return NextResponse.json(
        { error: "Failed to delete account data" },
        { status: 500 }
      );
    }

    // Step 3: Delete the user account using Better Auth
    try {
      // Use Better Auth's deleteUser method which will handle the user table deletion
      await auth.api.deleteUser({
        headers: headers(),
        body: { password }
      });

      console.log(`Successfully deleted user account: ${userId}`);

    } catch (error: any) {
      console.error('Error deleting user account:', error);
      return NextResponse.json(
        { error: error.message || "Failed to delete user account" },
        { status: 500 }
      );
    }

    // Step 4: Log successful deletion for audit purposes
    console.log('Account deletion completed:', {
      userId,
      email: userEmail,
      deletedAt: new Date().toISOString(),
      ip: request.headers.get('x-forwarded-for') || 'unknown'
    });

    return NextResponse.json({
      success: true,
      message: "Account permanently deleted"
    });

  } catch (error: any) {
    console.error('Account deletion error:', error);
    return NextResponse.json(
      { error: error.message || "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/account/delete
 * 
 * Get information about what data will be deleted
 * This helps users understand the scope of deletion
 */
export async function GET() {
  try {
    const session = await auth.api.getSession({ headers: headers() });
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" }, 
        { status: 401 }
      );
    }

    const userId = session.user.id;

    // Get counts of data that will be deleted
    const subscriptions = await subscriptionOperations.findByUserId(userId);

    const deletionInfo = {
      user: {
        email: session.user.email,
        name: session.user.name,
        createdAt: session.user.createdAt
      },
      dataToDelete: {
        subscriptions: subscriptions.length,
        activeSubscriptions: subscriptions.filter(s => s.status === 'active').length,
        sessions: "All active sessions",
        oauthAccounts: "All connected accounts"
      },
      deletionOptions: {
        hard: {
          description: "Permanently delete all data (GDPR compliant)",
          dataRemoved: "All personal data, subscriptions, purchases, and account records",
          warning: "This action cannot be undone. All your data will be permanently deleted.",
          recommended: true
        },
        soft: {
          description: "Anonymize personal data but retain business records",
          dataRemoved: "Personal information (email, name, image)",
          dataRetained: "Anonymized subscription and purchase records for legal/tax purposes",
          warning: "Your personal data will be anonymized but some business records may be retained."
        }
      }
    };

    return NextResponse.json(deletionInfo);

  } catch (error: any) {
    console.error('Error getting deletion info:', error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
