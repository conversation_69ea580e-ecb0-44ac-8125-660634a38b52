"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { authClient } from "@/lib/auth-client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { TerminalButton } from "@/components/ui/terminal-button";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Purchases } from "./purchases";
import { User, Mail, Calendar, Shield, LogOut, Loader2, Edit2, Save, X, Eye, EyeOff, Trash2, AlertTriangle } from "lucide-react";
import { ModernDashboardLayout } from "@/components/dashboard/modern-layout";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "sonner";

export default function AccountPage() {
  const router = useRouter();
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [signOutError, setSignOutError] = useState<string | null>(null);

  // Get user session data
  const { data: session, isPending, error, refetch } = authClient.useSession();

  // Edit states
  const [isEditingName, setIsEditingName] = useState(false);
  const [isEditingEmail, setIsEditingEmail] = useState(false);
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  // Form states
  const [editName, setEditName] = useState("");
  const [editEmail, setEditEmail] = useState("");
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  });

  // Loading states
  const [isUpdatingName, setIsUpdatingName] = useState(false);
  const [isUpdatingEmail, setIsUpdatingEmail] = useState(false);
  const [isUpdatingPassword, setIsUpdatingPassword] = useState(false);

  // Password visibility
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Delete account states
  const [showDeleteOptions, setShowDeleteOptions] = useState(false);
  const [softDeleteConfirmation, setSoftDeleteConfirmation] = useState("");
  const [hardDeleteConfirmation, setHardDeleteConfirmation] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);
  const [deletePassword, setDeletePassword] = useState("");
  const [showDeletePassword, setShowDeletePassword] = useState(false);
  const [deletionInfo, setDeletionInfo] = useState<any>(null);
  const [showDeletionPreview, setShowDeletionPreview] = useState(false);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    setSignOutError(null);

    try {
      await authClient.signOut({
        fetchOptions: {
          onSuccess: () => {
            // Redirect to home page after successful sign out
            router.push("/");
          },
          onError: (error) => {
            console.error("Sign out error:", error);
            setSignOutError("Failed to sign out. Please try again.");
            setIsSigningOut(false);
          }
        }
      });
    } catch (error) {
      console.error("Sign out error:", error);
      setSignOutError("Failed to sign out. Please try again.");
      setIsSigningOut(false);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit"
      });
    } catch {
      return "Unknown";
    }
  };

  // Update functions
  const handleUpdateName = async () => {
    if (!editName.trim()) {
      toast.error("Name cannot be empty");
      return;
    }

    setIsUpdatingName(true);
    try {
      await authClient.updateUser({
        name: editName.trim()
      });

      toast.success("Name updated successfully");
      setIsEditingName(false);
      refetch(); // Refresh session data
    } catch (error: any) {
      toast.error(error.message || "Failed to update name");
    } finally {
      setIsUpdatingName(false);
    }
  };

  const handleUpdateEmail = async () => {
    if (!editEmail.trim()) {
      toast.error("Email cannot be empty");
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(editEmail)) {
      toast.error("Please enter a valid email address");
      return;
    }

    setIsUpdatingEmail(true);
    try {
      await authClient.changeEmail({
        newEmail: editEmail.trim(),
        callbackURL: "/account"
      });

      toast.success("Email change verification sent. Please check your current email.");
      setIsEditingEmail(false);
    } catch (error: any) {
      toast.error(error.message || "Failed to update email");
    } finally {
      setIsUpdatingEmail(false);
    }
  };

  const handleChangePassword = async () => {
    if (!passwordForm.currentPassword) {
      toast.error("Current password is required");
      return;
    }

    if (!passwordForm.newPassword) {
      toast.error("New password is required");
      return;
    }

    if (passwordForm.newPassword.length < 8) {
      toast.error("New password must be at least 8 characters long");
      return;
    }

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast.error("New passwords do not match");
      return;
    }

    setIsUpdatingPassword(true);
    try {
      await authClient.changePassword({
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword,
        revokeOtherSessions: true
      });

      toast.success("Password changed successfully");
      setIsChangingPassword(false);
      setPasswordForm({
        currentPassword: "",
        newPassword: "",
        confirmPassword: ""
      });
    } catch (error: any) {
      toast.error(error.message || "Failed to change password");
    } finally {
      setIsUpdatingPassword(false);
    }
  };

  // Helper functions
  const startEditingName = () => {
    setEditName(user?.name || "");
    setIsEditingName(true);
  };

  const startEditingEmail = () => {
    setEditEmail(user?.email || "");
    setIsEditingEmail(true);
  };

  const cancelEditingName = () => {
    setIsEditingName(false);
    setEditName("");
  };

  const cancelEditingEmail = () => {
    setIsEditingEmail(false);
    setEditEmail("");
  };

  const cancelChangingPassword = () => {
    setIsChangingPassword(false);
    setPasswordForm({
      currentPassword: "",
      newPassword: "",
      confirmPassword: ""
    });
  };

  // Delete account functions
  const handleSoftDelete = async () => {
    if (softDeleteConfirmation !== "DEACTIVATE") {
      toast.error('Please type "DEACTIVATE" to confirm');
      return;
    }

    setIsDeleting(true);
    try {
      // For soft delete, we'll update the user's status to inactive
      // This requires a custom implementation since Better Auth doesn't have built-in soft delete
      await authClient.updateUser({
        // Add a custom field to mark as deactivated
        // Note: This would require extending the user schema
        name: `[DEACTIVATED] ${user?.name || user?.email}`,
      });

      toast.success("Account deactivated successfully. You can reactivate by contacting support.");

      // Sign out after deactivation
      setTimeout(() => {
        authClient.signOut({
          fetchOptions: {
            onSuccess: () => router.push("/")
          }
        });
      }, 2000);

    } catch (error: any) {
      toast.error(error.message || "Failed to deactivate account");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleHardDelete = async () => {
    if (hardDeleteConfirmation !== "DELETE FOREVER") {
      toast.error('Please type "DELETE FOREVER" to confirm');
      return;
    }

    if (!deletePassword) {
      toast.error("Password is required for permanent deletion");
      return;
    }

    setIsDeleting(true);
    try {
      // Use our custom deletion API that properly cleans up all data
      const response = await fetch('/api/account/delete', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          password: deletePassword,
          confirmation: hardDeleteConfirmation
        })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to delete account');
      }

      toast.success("Account and all data permanently deleted");

      // Sign out and redirect to home page
      setTimeout(() => {
        authClient.signOut({
          fetchOptions: {
            onSuccess: () => router.push("/")
          }
        });
      }, 2000);

    } catch (error: any) {
      toast.error(error.message || "Failed to delete account");
    } finally {
      setIsDeleting(false);
    }
  };

  const fetchDeletionInfo = async () => {
    try {
      const response = await fetch('/api/account/delete');
      if (response.ok) {
        const info = await response.json();
        setDeletionInfo(info);
        setShowDeletionPreview(true);
      }
    } catch (error) {
      console.error('Failed to fetch deletion info:', error);
    }
  };

  const resetDeleteForm = () => {
    setShowDeleteOptions(false);
    setSoftDeleteConfirmation("");
    setHardDeleteConfirmation("");
    setDeletePassword("");
    setShowDeletePassword(false);
    setShowDeletionPreview(false);
    setDeletionInfo(null);
  };

  if (isPending) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-mono text-neutral-200">Account</h1>
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3 text-neutral-400 font-mono">
            <Loader2 className="w-5 h-5 animate-spin" />
            <span>Loading account information...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-mono text-neutral-200">Account</h1>
        <Card className="border-red-800/50 bg-black/40 backdrop-blur">
          <CardContent className="p-6">
            <div className="flex items-center gap-3 text-red-400 font-mono">
              <Shield className="w-5 h-5" />
              <span>Error loading account: {error.message}</span>
            </div>
            <TerminalButton
              onClick={() => refetch()}
              className="mt-4"
              prompt="$"
              command="retry"
              path="/account"
            >
              Retry
            </TerminalButton>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!session?.user) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-mono text-neutral-200">Account</h1>
        <Card className="border-yellow-800/50 bg-black/40 backdrop-blur">
          <CardContent className="p-6">
            <div className="flex items-center gap-3 text-yellow-400 font-mono">
              <Shield className="w-5 h-5" />
              <span>No active session found</span>
            </div>
            <TerminalButton
              onClick={() => router.push("/signin")}
              className="mt-4"
              prompt="$"
              command="signin"
              path="/auth"
            >
              Sign In
            </TerminalButton>
          </CardContent>
        </Card>
      </div>
    );
  }

  const user = session.user;

  return (
    <ModernDashboardLayout
      title="Account"
      subtitle="Manage your profile and account settings"
    >
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Badge variant="default" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            Active Session
          </Badge>
        </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* User Profile Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
              <User className="w-5 h-5 text-[#ffbe98]" />
              Profile Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Name */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm text-neutral-600 dark:text-neutral-400">Name</label>
                {!isEditingName && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={startEditingName}
                    className="h-8 px-2 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
                  >
                    <Edit2 className="w-3 h-3" />
                  </Button>
                )}
              </div>

              <AnimatePresence mode="wait">
                {isEditingName ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.95 }}
                    transition={{ duration: 0.15, ease: "easeInOut" }}
                    className="space-y-3"
                  >
                    <div className="flex items-center gap-2">
                      <div className="flex-1 relative">
                        <User className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                        <Input
                          value={editName}
                          onChange={(e) => setEditName(e.target.value)}
                          placeholder="Enter your name"
                          className="pl-10"
                          disabled={isUpdatingName}
                        />
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        onClick={handleUpdateName}
                        disabled={isUpdatingName || !editName.trim()}
                        className="bg-[#ffbe98] hover:bg-[#ffbe98]/90 text-white"
                      >
                        {isUpdatingName ? (
                          <Loader2 className="w-3 h-3 animate-spin" />
                        ) : (
                          <Save className="w-3 h-3" />
                        )}
                        {isUpdatingName ? "Saving..." : "Save"}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={cancelEditingName}
                        disabled={isUpdatingName}
                      >
                        <X className="w-3 h-3" />
                        Cancel
                      </Button>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.15, ease: "easeInOut" }}
                    className="flex items-center gap-3 p-3 rounded-lg bg-neutral-50 dark:bg-neutral-800/50 border border-neutral-200 dark:border-neutral-700"
                  >
                    <User className="w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                    <span className="text-neutral-900 dark:text-neutral-200">
                      {user.name || "Not provided"}
                    </span>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Email */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm text-neutral-600 dark:text-neutral-400">Email Address</label>
                {!isEditingEmail && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={startEditingEmail}
                    className="h-8 px-2 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
                  >
                    <Edit2 className="w-3 h-3" />
                  </Button>
                )}
              </div>

              <AnimatePresence mode="wait">
                {isEditingEmail ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.95 }}
                    transition={{ duration: 0.15, ease: "easeInOut" }}
                    className="space-y-3"
                  >
                    <div className="flex items-center gap-2">
                      <div className="flex-1 relative">
                        <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                        <Input
                          type="email"
                          value={editEmail}
                          onChange={(e) => setEditEmail(e.target.value)}
                          placeholder="Enter your email"
                          className="pl-10"
                          disabled={isUpdatingEmail}
                        />
                      </div>
                    </div>
                    <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-3">
                      <p className="text-sm text-blue-700 dark:text-blue-300">
                        A verification email will be sent to your current email address to confirm this change.
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        onClick={handleUpdateEmail}
                        disabled={isUpdatingEmail || !editEmail.trim()}
                        className="bg-[#ffbe98] hover:bg-[#ffbe98]/90 text-white"
                      >
                        {isUpdatingEmail ? (
                          <Loader2 className="w-3 h-3 animate-spin" />
                        ) : (
                          <Save className="w-3 h-3" />
                        )}
                        {isUpdatingEmail ? "Sending..." : "Send Verification"}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={cancelEditingEmail}
                        disabled={isUpdatingEmail}
                      >
                        <X className="w-3 h-3" />
                        Cancel
                      </Button>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.15, ease: "easeInOut" }}
                    className="flex items-center gap-3 p-3 rounded-lg bg-neutral-50 dark:bg-neutral-800/50 border border-neutral-200 dark:border-neutral-700"
                  >
                    <Mail className="w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                    <span className="text-neutral-900 dark:text-neutral-200">{user.email}</span>
                    {user.emailVerified && (
                      <Badge variant="default" className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                        Verified
                      </Badge>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Change Password */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm text-neutral-600 dark:text-neutral-400">Password</label>
                {!isChangingPassword && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsChangingPassword(true)}
                    className="h-8 px-2 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
                  >
                    <Edit2 className="w-3 h-3" />
                  </Button>
                )}
              </div>

              <AnimatePresence mode="wait">
                {isChangingPassword ? (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.95 }}
                    transition={{ duration: 0.15, ease: "easeInOut" }}
                    className="space-y-3"
                  >
                    {/* Current Password */}
                    <div className="relative">
                      <Shield className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                      <Input
                        type={showCurrentPassword ? "text" : "password"}
                        value={passwordForm.currentPassword}
                        onChange={(e) => setPasswordForm(prev => ({ ...prev, currentPassword: e.target.value }))}
                        placeholder="Current password"
                        className="pl-10 pr-10"
                        disabled={isUpdatingPassword}
                      />
                      <button
                        type="button"
                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300"
                        disabled={isUpdatingPassword}
                      >
                        {showCurrentPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>

                    {/* New Password */}
                    <div className="relative">
                      <Shield className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                      <Input
                        type={showNewPassword ? "text" : "password"}
                        value={passwordForm.newPassword}
                        onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                        placeholder="New password (min. 8 characters)"
                        className="pl-10 pr-10"
                        disabled={isUpdatingPassword}
                      />
                      <button
                        type="button"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300"
                        disabled={isUpdatingPassword}
                      >
                        {showNewPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>

                    {/* Confirm Password */}
                    <div className="relative">
                      <Shield className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                      <Input
                        type={showConfirmPassword ? "text" : "password"}
                        value={passwordForm.confirmPassword}
                        onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        placeholder="Confirm new password"
                        className="pl-10 pr-10"
                        disabled={isUpdatingPassword}
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300"
                        disabled={isUpdatingPassword}
                      >
                        {showConfirmPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </button>
                    </div>

                    <div className="bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800 rounded-lg p-3">
                      <p className="text-sm text-amber-700 dark:text-amber-300">
                        Changing your password will sign you out of all other devices for security.
                      </p>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        onClick={handleChangePassword}
                        disabled={isUpdatingPassword || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword}
                        className="bg-[#ffbe98] hover:bg-[#ffbe98]/90 text-white"
                      >
                        {isUpdatingPassword ? (
                          <Loader2 className="w-3 h-3 animate-spin" />
                        ) : (
                          <Save className="w-3 h-3" />
                        )}
                        {isUpdatingPassword ? "Changing..." : "Change Password"}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={cancelChangingPassword}
                        disabled={isUpdatingPassword}
                      >
                        <X className="w-3 h-3" />
                        Cancel
                      </Button>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.15, ease: "easeInOut" }}
                    className="flex items-center gap-3 p-3 rounded-lg bg-neutral-50 dark:bg-neutral-800/50 border border-neutral-200 dark:border-neutral-700"
                  >
                    <Shield className="w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                    <span className="text-neutral-900 dark:text-neutral-200">••••••••</span>
                    <Badge variant="default" className="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                      Protected
                    </Badge>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Account Created */}
            <div className="space-y-2">
              <label className="text-sm text-neutral-600 dark:text-neutral-400">Account Created</label>
              <div className="flex items-center gap-3 p-3 rounded-lg bg-neutral-50 dark:bg-neutral-800/50 border border-neutral-200 dark:border-neutral-700">
                <Calendar className="w-4 h-4 text-neutral-500 dark:text-neutral-400" />
                <span className="text-neutral-900 dark:text-neutral-200">
                  {user.createdAt ? formatDate(user.createdAt.toISOString()) : "Unknown"}
                </span>
              </div>
            </div>


          </CardContent>
        </Card>

        {/* Account Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
              <Shield className="w-5 h-5 text-[#ffbe98]" />
              Account Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <p className="text-sm text-neutral-600 dark:text-neutral-400">
                Manage your account security and session
              </p>

              {/* Sign Out Button */}
              <div className="space-y-3">
                <TerminalButton
                  onClick={handleSignOut}
                  disabled={isSigningOut}
                  className="w-full justify-center bg-red-950/20 border-red-800/50 hover:bg-red-950/30 hover:border-red-700/50"
                  prompt="$"
                  command={isSigningOut ? "signing-out..." : "signout"}
                  path="/auth"
                >
                  <div className="flex items-center gap-2">
                    {isSigningOut ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <LogOut className="w-4 h-4" />
                    )}
                    {isSigningOut ? "Signing out..." : "Sign Out"}
                  </div>
                </TerminalButton>

                {signOutError && (
                  <div className="text-red-400 font-mono text-xs p-2 rounded bg-red-950/20 border border-red-800/50">
                    {signOutError}
                  </div>
                )}

                <p className="text-xs font-mono text-neutral-500">
                  This will end your current session and redirect you to the home page.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Delete Account Section */}
        <Card className="border-red-200 dark:border-red-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-red-600 dark:text-red-400">
              <Shield className="w-5 h-5" />
              Danger Zone
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              These actions will affect your account permanently. Please read carefully before proceeding.
            </p>

            {/* Delete Options Toggle */}
            {!showDeleteOptions ? (
              <Button
                variant="outline"
                onClick={() => setShowDeleteOptions(true)}
                className="w-full border-red-200 dark:border-red-800 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-950/20"
              >
                Manage Account Deletion
              </Button>
            ) : (
              <div className="space-y-6">
                {/* Soft Delete Option */}
                <div className="p-4 border border-orange-200 dark:border-orange-800 rounded-lg bg-orange-50 dark:bg-orange-950/20">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0" />
                      <div className="flex-1">
                        <h4 className="font-medium text-orange-800 dark:text-orange-200">
                          Deactivate Account (Soft Delete)
                        </h4>
                        <p className="text-sm text-orange-700 dark:text-orange-300 mt-1">
                          Temporarily disable your account. Your data will be preserved and you can reactivate by contacting support.
                        </p>
                        <ul className="text-xs text-orange-600 dark:text-orange-400 mt-2 space-y-1 ml-4">
                          <li>• Account becomes inaccessible</li>
                          <li>• All data is preserved</li>
                          <li>• Can be reactivated by support</li>
                          <li>• Subscriptions are paused</li>
                        </ul>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Input
                        placeholder='Type "DEACTIVATE" to confirm'
                        value={softDeleteConfirmation}
                        onChange={(e) => setSoftDeleteConfirmation(e.target.value)}
                        disabled={isDeleting}
                        className="border-orange-300 dark:border-orange-700"
                      />
                      <Button
                        onClick={handleSoftDelete}
                        disabled={isDeleting || softDeleteConfirmation !== "DEACTIVATE"}
                        className="w-full bg-orange-600 hover:bg-orange-700 text-white"
                      >
                        {isDeleting ? (
                          <Loader2 className="w-4 h-4 animate-spin mr-2" />
                        ) : null}
                        {isDeleting ? "Deactivating..." : "Deactivate Account"}
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Hard Delete Option */}
                <div className="p-4 border border-red-200 dark:border-red-800 rounded-lg bg-red-50 dark:bg-red-950/20">
                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0" />
                      <div className="flex-1">
                        <h4 className="font-medium text-red-800 dark:text-red-200">
                          Delete Account Permanently (Hard Delete)
                        </h4>
                        <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                          Permanently delete your account and all associated data. This action cannot be undone.
                        </p>
                        <ul className="text-xs text-red-600 dark:text-red-400 mt-2 space-y-1 ml-4">
                          <li>• All data is permanently deleted</li>
                          <li>• Cannot be recovered</li>
                          <li>• Subscriptions are cancelled</li>
                          <li>• Purchase history is removed</li>
                        </ul>

                        {!showDeletionPreview && (
                          <Button
                            onClick={fetchDeletionInfo}
                            variant="outline"
                            size="sm"
                            className="mt-3 border-red-300 text-red-700 hover:bg-red-50 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-950/30"
                          >
                            Preview What Will Be Deleted
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Deletion Preview */}
                    {showDeletionPreview && deletionInfo && (
                      <div className="mt-4 p-3 bg-red-100 dark:bg-red-950/40 rounded border border-red-200 dark:border-red-800">
                        <h5 className="font-medium text-red-800 dark:text-red-200 mb-2">
                          Data to be permanently deleted:
                        </h5>
                        <div className="text-xs text-red-700 dark:text-red-300 space-y-1">
                          <div>• Account: {deletionInfo.user.email}</div>
                          <div>• Subscriptions: {deletionInfo.dataToDelete.subscriptions} total ({deletionInfo.dataToDelete.activeSubscriptions} active)</div>
                          <div>• Purchases: {deletionInfo.dataToDelete.purchases}</div>
                          <div>• Sessions: {deletionInfo.dataToDelete.sessions}</div>
                          <div>• OAuth Accounts: {deletionInfo.dataToDelete.oauthAccounts}</div>
                        </div>
                        <p className="text-xs text-red-600 dark:text-red-400 mt-2 font-medium">
                          ⚠️ {deletionInfo.warning}
                        </p>
                      </div>
                    )}

                    <div className="space-y-3">
                      <Input
                        placeholder='Type "DELETE FOREVER" to confirm'
                        value={hardDeleteConfirmation}
                        onChange={(e) => setHardDeleteConfirmation(e.target.value)}
                        disabled={isDeleting}
                        className="border-red-300 dark:border-red-700"
                      />
                      <div className="relative">
                        <Input
                          type={showDeletePassword ? "text" : "password"}
                          placeholder="Enter your password"
                          value={deletePassword}
                          onChange={(e) => setDeletePassword(e.target.value)}
                          disabled={isDeleting}
                          className="border-red-300 dark:border-red-700 pr-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowDeletePassword(!showDeletePassword)}
                          className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-400 hover:text-neutral-600 dark:hover:text-neutral-300"
                          disabled={isDeleting}
                        >
                          {showDeletePassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                        </button>
                      </div>
                      <Button
                        onClick={handleHardDelete}
                        disabled={isDeleting || hardDeleteConfirmation !== "DELETE FOREVER" || !deletePassword}
                        className="w-full bg-red-600 hover:bg-red-700 text-white"
                      >
                        {isDeleting ? (
                          <Loader2 className="w-4 h-4 animate-spin mr-2" />
                        ) : null}
                        {isDeleting ? "Deleting..." : "Delete Account Forever"}
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Cancel Button */}
                <Button
                  variant="outline"
                  onClick={resetDeleteForm}
                  disabled={isDeleting}
                  className="w-full"
                >
                  Cancel
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

        {/* Purchases Section */}
        <div className="space-y-4">
          <h2 className="text-xl text-neutral-900 dark:text-neutral-200">Purchase History</h2>
          <Purchases />
        </div>
      </div>
    </ModernDashboardLayout>
  );
}